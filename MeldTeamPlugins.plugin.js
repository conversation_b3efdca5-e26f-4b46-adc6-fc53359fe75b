/**
 * @name MeldTeamPlugins
 * @version 0.1.4
 * @description Загрузчик для набора плагинов Meld Team (динамический список с сервера).
 * <AUTHOR>
 * @source https://github.com/YourRepo/NotesPlugin
 * @website https://betterdiscord.app/
 */

module.exports = class MeldTeamPlugins {
    constructor() {
        this.pluginInstances = [];
        this.pluginBaseUrl = 'https://meld-team-discord.vercel.app/';
        this.manifestUrl = `${this.pluginBaseUrl}plugins-manifest.json`; // URL манифеста
        this.pluginNamespace = 'MeldTeamLoadedPlugins';

        // Убираем жестко закодированные списки
        this.availablePlugins = []; // Загрузится из манифеста
        this.pluginSettings = {};   // Загрузится из BdApi и смержится
        this.defaultPluginSettings = {}; // Сформируется из манифеста

        // Состояние загрузки манифеста
        this.manifestLoading = false;
        this.manifestLoaded = false;
        this.manifestError = null;
    }

    // --- ЗАГРУЗКА МАНИФЕСТА И НАСТРОЕК ---

    async fetchManifestAndPrepareSettings() {
        if (this.manifestLoading) return; // Уже грузим
        if (this.manifestLoaded) return;   // Уже загружено

        this.manifestLoading = true;
        this.manifestError = null;
        console.log("MeldTeamLoader: Загрузка манифеста плагинов...");
        BdApi.showToast("MeldTeam Plugins: Загрузка списка плагинов...", { type: "info", timeout: 2000 });

        try {
            const response = await fetch(`${this.manifestUrl}?_=${Date.now()}`, { cache: "no-store" });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status} при загрузке манифеста`);
            }
            const manifestData = await response.json();

            if (!Array.isArray(manifestData)) {
                 throw new Error("Манифест должен быть массивом JSON");
            }

            // Валидация базовых полей
            this.availablePlugins = manifestData.filter(p => p && p.name && p.file);
            if (this.availablePlugins.length !== manifestData.length) {
                 console.warn("MeldTeamLoader: Некоторые записи в манифесте были некорректны и пропущены.");
            }

            console.log(`MeldTeamLoader: Манифест успешно загружен. Доступно плагинов: ${this.availablePlugins.length}`);

            // Формируем дефолтные настройки из манифеста
            this.defaultPluginSettings = {};
            for (const pluginInfo of this.availablePlugins) {
                this.defaultPluginSettings[pluginInfo.name] = pluginInfo.defaultEnabled === true; // По умолчанию true, если не указано false
            }

            // Загружаем сохраненные настройки пользователя
            const savedSettings = BdApi.loadData(this.getName(), "pluginSettings") || {};

            // Мержим настройки: приоритет у сохраненных, но учитываем только доступные плагины
            this.pluginSettings = {};
            for (const pluginInfo of this.availablePlugins) {
                 const name = pluginInfo.name;
                 if (savedSettings.hasOwnProperty(name)) {
                     this.pluginSettings[name] = savedSettings[name];
                } else {
                     // Если сохраненной настройки нет, берем из дефолтных (сформированных из манифеста)
                     this.pluginSettings[name] = this.defaultPluginSettings[name];
                 }
            }

            this.manifestLoaded = true;
             BdApi.showToast(`MeldTeam Plugins: Список плагинов загружен (${this.availablePlugins.length}).`, { type: "success" });


        } catch (error) {
            console.error("MeldTeamLoader: Ошибка загрузки или обработки манифеста:", error);
            this.manifestError = error.message || "Неизвестная ошибка";
            BdApi.showToast(`MeldTeam Plugins: Ошибка загрузки списка плагинов! ${this.manifestError}`, { type: "error", timeout: 7000 });
            this.availablePlugins = []; // Очищаем список при ошибке
            this.manifestLoaded = false; // Считаем, что не загружен
        } finally {
             this.manifestLoading = false;
        }
    }

    // Сохраняем только актуальные настройки для доступных плагинов
    saveSettings() {
        const settingsToSave = {};
        for(const pluginInfo of this.availablePlugins) {
            if (this.pluginSettings.hasOwnProperty(pluginInfo.name)) {
                 settingsToSave[pluginInfo.name] = this.pluginSettings[pluginInfo.name];
            }
        }
        BdApi.saveData(this.getName(), "pluginSettings", settingsToSave);
        console.log("MeldTeamLoader: Настройки сохранены:", settingsToSave);
        this.reloadPlugins(); // Перезагружаем плагины
    }

    // --- ПАНЕЛЬ НАСТРОЕК ---
    getSettingsPanel() {
        // Сначала проверяем ZLibrary
        if (!global.ZeresPluginLibrary) {
             // Код для отображения ошибки ZLibrary (без изменений)
            const panel = document.createElement("div");
            panel.style.padding = "20px";
            panel.style.color = "var(--text-normal)";
            panel.innerHTML = `
                <h2>Требуется ZeresPluginLibrary</h2>
                <p>Для отображения настроек этого плагина необходима <a href="https://betterdiscord.app/plugin/ZeresPluginLibrary" target="_blank" style="color: var(--text-link);">ZeresPluginLibrary</a>.</p>
                <p>Пожалуйста, установите ее и перезапустите Discord или плагин.</p>
            `;
            return panel;
        }

        const { Settings } = ZLibrary;
        const settingItems = [];

        // Проверяем состояние загрузки манифеста
        if (this.manifestLoading) {
             settingItems.push(this.createNoticeElement("Загрузка списка плагинов с сервера..."));
        } else if (this.manifestError) {
             settingItems.push(this.createNoticeElement(`Ошибка загрузки списка плагинов: ${this.manifestError}`, 'error'));
             // Кнопка для повторной попытки загрузки манифеста
             settingItems.push(
                 new Settings.Button("Попробовать снова", "Загрузить список плагинов с сервера", async () => {
                     await this.fetchManifestAndPrepareSettings();
                     // Нужно как-то обновить панель настроек здесь.
                     // Простейший вариант - попросить пользователя переоткрыть настройки.
                     BdApi.showToast("Повторная загрузка завершена. Переоткройте настройки.", { type: "info" });
                 }, { style: Settings.Button.Styles.PRIMARY })
             );
        } else if (!this.manifestLoaded || this.availablePlugins.length === 0) {
             settingItems.push(this.createNoticeElement("Список доступных плагинов пуст или не загружен.", 'warn'));
        } else {
            // Манифест загружен, создаем переключатели
             for (const pluginInfo of this.availablePlugins) {
                  settingItems.push(
                      new Settings.Switch(
                          pluginInfo.name,
                          pluginInfo.description || `Включить/выключить плагин ${pluginInfo.name}`,
                          this.pluginSettings[pluginInfo.name] === true,
                          (checked) => {
                              this.pluginSettings[pluginInfo.name] = checked;
                          }
                      )
                  );
             }
        }

        return Settings.SettingPanel.build(
            () => { if (this.manifestLoaded) this.saveSettings(); }, // Сохраняем только если манифест был загружен
            ...settingItems
        );
    }

    // Вспомогательный метод для создания уведомлений в настройках
    createNoticeElement(text, type = 'info') {
         const notice = document.createElement("p");
         notice.textContent = text;
         notice.style.padding = "10px";
         notice.style.borderRadius = "5px";
         notice.style.marginBottom = "10px";
         switch(type) {
             case 'error':
                 notice.style.backgroundColor = "var(--background-danger)";
                 notice.style.border = "1px solid var(--status-danger)";
                 notice.style.color = "var(--text-normal)";
                 break;
             case 'warn':
                 notice.style.backgroundColor = "var(--background-warning)";
                 notice.style.border = "1px solid var(--status-warning)";
                 notice.style.color = "var(--text-normal)";
                 break;
             default: // info
                 notice.style.backgroundColor = "var(--background-secondary)";
                 notice.style.color = "var(--text-muted)";
         }
         return notice;
    }


    // --- ЛОГИКА ЗАГРУЗКИ И ЗАПУСКА (Минимальные изменения) ---
    async loadPlugins() {
        // Добавляем проверку на загрузку манифеста
        if (!this.manifestLoaded || this.availablePlugins.length === 0) {
             console.log("MeldTeamLoader: Манифест не загружен или пуст. Загрузка плагинов невозможна.");
             return false;
        }

        console.log(`MeldTeamLoader: Проверка и загрузка выбранных плагинов...`);
        BdApi.showToast("MeldTeam Plugins: Загрузка плагинов...", { type: "info" });

        const pluginsToLoadNow = this.availablePlugins.filter(p => this.pluginSettings[p.name] === true);

        if (pluginsToLoadNow.length === 0) {
             console.log("MeldTeamLoader: Нет выбранных плагинов для загрузки.");
             BdApi.showToast("MeldTeam Plugins: Нет выбранных плагинов.", { type: "info" });
             return false;
        }
        // ... остальная часть метода loadPlugins без изменений ...
        // (Цикл fetch, eval, проверка результата, сообщение BdApi)
        console.log(`MeldTeamLoader: Планируется загрузка ${pluginsToLoadNow.length} плагинов:`, pluginsToLoadNow.map(p => p.name));

        window[this.pluginNamespace] = {};
        let successfulLoads = 0;

        for (const pluginInfo of pluginsToLoadNow) {
            const fetchUrl = `${this.pluginBaseUrl}${pluginInfo.file}?_=${Date.now()}`;
            let isSuccess = false;

            try {
                console.log(`MeldTeamLoader: Загрузка ${pluginInfo.name} с ${fetchUrl}...`);
                const response = await fetch(fetchUrl, { cache: "no-store" });
                if (!response.ok) throw new Error(`HTTP ${response.status} для ${pluginInfo.file}`);
                const scriptContent = await response.text();

                try {
                     const modifiedScriptContent = `
                        ${scriptContent}
                        // --- Добавлено загрузчиком MeldTeam ---
                        try {
                             if (typeof ${pluginInfo.name} === 'function') {
                                 if (!window.${this.pluginNamespace}) { window.${this.pluginNamespace} = {}; }
                                 window.${this.pluginNamespace}.${pluginInfo.name} = ${pluginInfo.name};
                                 console.log('MeldTeamLoader (eval): Класс ${pluginInfo.name} успешно зарегистрирован в ${this.pluginNamespace}.');
                             } else {
                                 console.error('MeldTeamLoader (eval): Класс ${pluginInfo.name} не был определен после выполнения кода.');
                             }
                        } catch (e) {
                             console.error('MeldTeamLoader (eval): Ошибка при попытке регистрации ${pluginInfo.name}:', e);
                        }
                        // --- Конец добавленного кода ---
                    `;
                     eval(modifiedScriptContent);
                     if (window[this.pluginNamespace] && typeof window[this.pluginNamespace][pluginInfo.name] === 'function') {
                        isSuccess = true;
                    } else {
                         isSuccess = false;
                    }
                } catch(e) {
                     console.error(`MeldTeamLoader: Ошибка при eval скрипта ${pluginInfo.name}:`, e);
                     isSuccess = false;
                }

                 if(isSuccess) successfulLoads++;
                 else BdApi.showToast(`MeldTeam Plugins: Ошибка регистрации ${pluginInfo.name}.`, { type: "error" });

            } catch (error) {
                console.error(`MeldTeamLoader: Не удалось загрузить или выполнить ${pluginInfo.name}:`, error);
                BdApi.showToast(`MeldTeam Plugins: Ошибка загрузки/eval ${pluginInfo.file}`, { type: "error", timeout: 4000 });
            }
        }

         const totalAttempted = pluginsToLoadNow.length;
         // ... Сообщение BdApi о результате ...
         let message = `Загрузка завершена. Успешно загружено ${successfulLoads} из ${totalAttempted}.`;
         let type = "info";
         if (successfulLoads === totalAttempted && totalAttempted > 0) { // Добавили проверку totalAttempted > 0
             message = `${totalAttempted} плагина(ов) успешно загружены.`;
             type = "success";
         } else if (successfulLoads > 0) {
              message = `Загружено ${successfulLoads} из ${totalAttempted} плагинов. Проверьте консоль.`;
              type = "warning";
         } else if (totalAttempted > 0) { // Только если пытались загрузить
              message = `Не удалось загрузить ни один из ${totalAttempted} выбранных плагинов.`;
              type = "error";
         } else {
              // Случай, когда не было выбрано плагинов, уже обработан в начале
              return false;
         }
         BdApi.showToast(message, { type: type, timeout: (type === "success") ? 3000 : 5000 });

         return successfulLoads > 0;
    }

    // --- startPlugins, stopPlugins, reloadPlugins (Без изменений) ---
     startPlugins() { /* ... */
            const loadedClasses = window[this.pluginNamespace];
        if (!loadedClasses || Object.keys(loadedClasses).length === 0) {
            console.log("MeldTeamLoader: Нет загруженных классов для запуска.");
            return; // Нечего запускать
        }

        console.log("MeldTeamLoader: Запуск загруженных плагинов...");
        let startedCount = 0;

        // Итерируем по ЗАГРУЖЕННЫМ классам в неймспейсе
        for (const className in loadedClasses) {
            if (loadedClasses.hasOwnProperty(className) && typeof loadedClasses[className] === 'function') {
                 // Проверяем, не запущен ли уже экземпляр (на всякий случай)
                 if (this.pluginInstances.some(p => p.name === className)) {
                     console.warn(`MeldTeamLoader: Экземпляр ${className} уже существует. Пропуск запуска.`);
                     continue;
                 }

                    try {
                        console.log(`MeldTeamLoader: Создание экземпляра ${className}...`);
                        const instance = new loadedClasses[className]();
                     this.pluginInstances.push({ name: className, instance: instance });

                        if (typeof instance.start === 'function') {
                             console.log(`MeldTeamLoader: Запуск start() для ${className}...`);
                             instance.start();
                             console.log(`MeldTeamLoader: ${className} успешно запущен.`);
                         startedCount++;
                        } else {
                            console.warn(`MeldTeamLoader: У класса ${className} нет метода start().`);
                        }
                    } catch (error) {
                        console.error(`MeldTeamLoader: Ошибка при инициализации или запуске ${className}:`, error);
                        BdApi.showToast(`MeldTeam Plugins: Ошибка запуска ${className}.`, { type: "error" });
                      // Удаляем неудачный экземпляр из списка
                     this.pluginInstances = this.pluginInstances.filter(p => p.name !== className);
                 }
            }
        }
        console.log(`MeldTeamLoader: Запуск завершен. Активно ${this.pluginInstances.length} плагинов.`);
     }
     stopPlugins() { /* ... */
        console.log("MeldTeamLoader: Остановка активных плагинов...");
        if (this.pluginInstances.length === 0) {
            console.log("MeldTeamLoader: Нет активных плагинов для остановки.");
            return;
        }

        // Используем обратный цикл, чтобы безопасно удалять элементы
        for (let i = this.pluginInstances.length - 1; i >= 0; i--) {
            const pluginData = this.pluginInstances[i];
            try {
                if (pluginData.instance && typeof pluginData.instance.stop === 'function') {
                    console.log(`MeldTeamLoader: Остановка ${pluginData.name}...`);
                    pluginData.instance.stop();
                }
            } catch (error) {
                console.error(`MeldTeamLoader: Ошибка при остановке ${pluginData.name}:`, error);
            }
        }
        const stoppedCount = this.pluginInstances.length;
        this.pluginInstances = []; // Очищаем массив
        console.log(`MeldTeamLoader: Остановлено ${stoppedCount} плагинов.`);

        // Очищаем неймспейс
        if (window[this.pluginNamespace]) {
             delete window[this.pluginNamespace];
             console.log("MeldTeamLoader: Неймспейс плагинов очищен.");
        }
     }
     async reloadPlugins() { /* ... */
         console.log("MeldTeamLoader: Перезагрузка плагинов из-за изменения настроек...");
         this.stopPlugins(); // Останавливаем текущие
         // Небольшая пауза для завершения асинхронных операций остановки, если они есть
         await new Promise(resolve => setTimeout(resolve, 100));
         const anyLoaded = await this.loadPlugins(); // Загружаем выбранные
         if (anyLoaded) {
             this.startPlugins(); // Запускаем загруженные
         }
         console.log("MeldTeamLoader: Перезагрузка завершена.");
     }

    // --- МЕТОДЫ ЖИЗНЕННОГО ЦИКЛА ПЛАГИНА BD ---
    getName() { return "MeldTeamPlugins"; }

    load() {
        // Загружаем только сохраненные настройки. Мерж будет после загрузки манифеста.
        this.pluginSettings = BdApi.loadData(this.getName(), "pluginSettings") || {};
        console.log("MeldTeamLoader: Предварительно загружены сохраненные настройки:", this.pluginSettings);
        // Проверка ZLibrary (без изменений)
        if (!global.ZeresPluginLibrary) {
            BdApi.showToast(
                "MeldTeamPlugins: ZeresPluginLibrary не найдена. Настройки будут недоступны.",
                { type: "warning", timeout: 7000 }
            );
             // Показываем алерт при первой загрузке, если библиотеки нет
             BdApi.alert(
                 "Требуется ZeresPluginLibrary",
                 "Для работы **настроек** плагина MeldTeamPlugins требуется библиотека ZeresPluginLibrary. Основная функциональность загрузки плагинов может работать, но панель настроек будет недоступна. Пожалуйста, скачайте и установите ZeresPluginLibrary с сайта BetterDiscord."
             );
        }
    }

    async start() { // Делаем start асинхронным
        console.log("MeldTeamLoader: Запуск основного загрузчика...");
        await this.fetchManifestAndPrepareSettings(); // Ждем загрузки манифеста и подготовки настроек

        // Запускаем загрузку и старт плагинов только если манифест успешно загружен
        if (this.manifestLoaded) {
             const anyLoaded = await this.loadPlugins(); // Ждем загрузки
             if (anyLoaded) {
                 this.startPlugins(); // Запускаем
             }
        } else {
             console.error("MeldTeamLoader: Не удалось загрузить манифест, запуск плагинов невозможен.");
        }
    }

    stop() {
        console.log("MeldTeamLoader: Остановка основного загрузчика...");
        this.stopPlugins();
        // Сбрасываем состояние загрузки манифеста при остановке
        this.manifestLoaded = false;
        this.manifestLoading = false;
        this.manifestError = null;
        this.availablePlugins = [];
        BdApi.showToast("MeldTeam Plugins: Загрузчик остановлен.", { type: "info" });
    }
};

// --- ВАЖНО: НЕ УДАЛЯЙТЕ ЭТУ СТРОКУ ---
if (!global.ZeresPluginLibrary) { module.exports.prototype.load = () => BdApi.alert("Отсутствует ZeresPluginLibrary", "Для работы некоторых плагинов MeldTeam может требоваться библиотека ZeresPluginLibrary. Пожалуйста, скачайте и установите ее."); }
// --- КОНЕЦ ВАЖНОЙ СТРОКИ ---
